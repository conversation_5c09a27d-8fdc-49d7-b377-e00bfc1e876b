server {
  listen 80;
  client_max_body_size 10G;
  # 所有非 /api 请求全部代理给 frontend 容器
  location / {
    proxy_pass http://frontend:80;
  }

  # 所有 /api 请求代理给 backend 容器
  location /api/ {
    proxy_pass http://backend:8000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
  }
    
   location /static/ {
    proxy_pass http://backend:8000/static/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;

  }
}
