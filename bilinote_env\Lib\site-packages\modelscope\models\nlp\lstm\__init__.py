# Copyright (c) Alibaba, Inc. and its affiliates.
from typing import TYPE_CHECKING

from modelscope.utils.import_utils import LazyImportModule

if TYPE_CHECKING:
    from .backbone import LSTMModel
    from .token_classification import LSTMForTokenClassificationWithCRF
else:
    _import_structure = {
        'backbone': ['LSTM'],
        'token_classification': ['LSTMForTokenClassificationWithCRF'],
    }

    import sys

    sys.modules[__name__] = LazyImportModule(
        __name__,
        globals()['__file__'],
        _import_structure,
        module_spec=__spec__,
        extra_objects={},
    )
